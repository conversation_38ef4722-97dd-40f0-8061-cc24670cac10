import { getAgentsListFromD1, getRoundRobinIndexFromD1, setRoundRobinIndexInD1 } from './db/meta';

export async function getNextAgentRoundRobin(env) {
  const agentsList = await getAgentsListFromD1(env);

  if (!agentsList || agentsList.length === 0) {
    throw new Error('No agents available for round-robin assignment');
  }

  const currentIndex = await getRoundRobinIndexFromD1(env);

  console.log('getting agent from round robin (D1), index:', currentIndex);
  console.log('round-robin agents (D1):', agentsList.map((o) => o.email.split('@')[0]).join(', '));

  const agent = {
    ...agentsList[currentIndex],
    assigned_at: new Date().toISOString(),
  };

  const nextIndex = (currentIndex + 1) % agentsList.length;
  await setRoundRobinIndexInD1(env, nextIndex);

  return agent;
}
