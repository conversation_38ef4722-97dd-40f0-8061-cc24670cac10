export async function getAgentsListFromD1(env) {
  const agentsList = await getMetaValue(env, 'agents:list');

  if (!agentsList || !Array.isArray(agentsList) || agentsList.length === 0) {
    return env.SAMPLE_AGENTS || [];
  }

  return agentsList;
}

export async function setAgentsListInD1(env, agentsList) {
  if (!Array.isArray(agentsList)) {
    throw new Error('Agents list must be an array');
  }

  await setMetaValue(env, 'agents:list', agentsList);
  return agentsList;
}

export async function getRoundRobinIndexFromD1(env) {
  const index = await getMetaValue(env, 'round-robin:index');
  return parseInt(index) || 0;
}

export async function setRoundRobinIndexInD1(env, index) {
  if (typeof index !== 'number' || index < 0) {
    throw new Error('Index must be a non-negative number');
  }

  await setMetaValue(env, 'round-robin:index', index.toString());
  return index;
}

export async function getMetaValue(env, key) {
  if (!key) return null;

  const statement = env.DB.prepare('SELECT value FROM meta WHERE key = ?').bind(key);
  const result = await statement.first();

  if (!result) return null;

  try {
    return JSON.parse(result.value);
  } catch (error) {
    return result.value;
  }
}

export async function setMetaValue(env, key, value) {
  if (!key) throw new Error('Missing key for meta update');

  const jsonValue = typeof value === 'string' ? value : JSON.stringify(value);

  const statement = env.DB.prepare(
    `INSERT INTO meta (key, value)
     VALUES (?, ?)
     ON CONFLICT(key) DO UPDATE SET value = ?, updated_at = datetime('now')`
  ).bind(key, jsonValue, jsonValue);

  await statement.run();
  return value;
}

export async function deleteMetaValue(env, key) {
  if (!key) return false;

  const statement = env.DB.prepare('DELETE FROM meta WHERE key = ?').bind(key);
  const result = await statement.run();

  return result.changes > 0;
}

export async function getAllMeta(env) {
  const statement = env.DB.prepare('SELECT key, value FROM meta');
  const result = await statement.all();

  const meta = {};
  for (const row of result.results) {
    try {
      meta[row.key] = JSON.parse(row.value);
    } catch (error) {
      meta[row.key] = row.value;
    }
  }

  return meta;
}
