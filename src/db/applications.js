export async function getApplicationByUUID(env, uuid) {
  if (!uuid) return null;

  const statement = env.DB.prepare('SELECT * FROM applications WHERE uuid = ?').bind(uuid);
  const result = await statement.first();

  return transformRawApplication(result);
}

export async function updateApplication(env, uuid, data, timestamp = null) {
  if (!uuid) throw new Error('Missing UUID for D1 update');
  if (!data) throw new Error('Missing data for D1 update');

  data.updated_at = timestamp || new Date().toISOString();

  const fields = [
    'version',
    'status',
    'domain',
    'preQualifyFields',
    'approvalAmount',
    'agent',
    'reason',
    'applicationFields',
    'pandadoc',
    'utm',
    'meta',
    'fastTrack',
    'salesforce_id',
    'created_at',
    'updated_at',
    'started_at',
    'submitted_at',
    'signed_at',
    'completed_at',
  ];

  const setClause = fields.map((field) => `${field} = ?`).join(', ');
  const values = fields.map((field) => {
    const value = data[field];

    if (field === 'fastTrack') {
      return value === true ? 1 : 0;
    }

    if (['preQualifyFields', 'applicationFields', 'pandadoc', 'utm', 'meta', 'agent'].includes(field)) {
      return value ? JSON.stringify(value) : null;
    }

    return value || null;
  });

  const statement = env.DB.prepare(
    `INSERT INTO applications (uuid, ${fields.join(', ')})
     VALUES (?, ${fields.map(() => '?').join(', ')})
     ON CONFLICT(uuid) DO UPDATE SET ${setClause}`
  ).bind(uuid, ...values, ...values);

  await statement.run();
  return data;
}

export async function getAllApplicationsFromD1(env) {
  const statement = env.DB.prepare('SELECT * FROM applications');
  const result = await statement.all();

  return result.results.map(transformRawApplication);
}

function parseJsonField(value) {
  if (value === null || value === undefined || value === 'NULL') {
    return null;
  }
  try {
    return JSON.parse(value);
  } catch (error) {
    console.error('Error parsing JSON field:', error);
    return null;
  }
}

function transformRawApplication(rawApp) {
  if (!rawApp) return null;

  return {
    ...rawApp,
    preQualifyFields: parseJsonField(rawApp.preQualifyFields),
    applicationFields: parseJsonField(rawApp.applicationFields),
    pandadoc: parseJsonField(rawApp.pandadoc),
    utm: parseJsonField(rawApp.utm),
    meta: parseJsonField(rawApp.meta),
    agent: parseJsonField(rawApp.agent),
    fastTrack: rawApp.fastTrack === 1 ? true : false,
  };
}
