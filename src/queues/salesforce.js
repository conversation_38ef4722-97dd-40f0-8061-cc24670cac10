import { SalesforceClient } from '../salesforce';
import { SalesforceUtils } from '../salesforce/utils';
import { getAppPII, getAppBankStatements } from '../kv';
import { downloadDocumentWithoutSignature } from '../pandadoc';
import { getAppSalesforceID, putAppSalesforceID } from '../utils/helpers';

const log = (...args) => console.log('SALESFORCE:\t', ...args);

/**
 * Handle Salesforce queue messages
 * @param {Object} message - The message object containing application data
 * @param {Object} env - Environment variables and bindings
 */
export async function salesforceQueueHandler(message, env) {
  log('salesforceQueueHandler called');

  try {
    const { application } = message;

    if (!application) {
      console.error('SALESFORCE:\t', 'No application data in message');
      return;
    }

    const {
      uuid,
      created_at,
      status,
      agent,
      domain,
      fastTrack,
      approvalAmount,
      reason,
      preQualifyFields: fields,
      applicationFields,
      utm,
      meta,
      meta: {
        initiated,
        initiated: { userAgent },
      },
      pandadoc,
    } = application;

    const appPortalUrl = domain ? `https://${domain}` : env.PORTAL_URL;
    const portalPath = status === 'PREQUAL_DENIED' ? 'prequalify-result' : 'application';

    const deviceInfo = [userAgent?.device?.vendor, userAgent?.device?.model, userAgent?.os?.name, userAgent?.os?.version];

    log('Processing Salesforce data for application:', uuid);
    log('Application Status:', status);

    // TODO: create a function to prepare the SF object from an application object
    //    * this should handle all the SF field names, normalization and conditional fields like owner 2
    //    * as well as overriding emails/business names on dev mode
    //    * it should use SalesforceUtils.getDealStage for Stage__c
    let Deal__c = {};
    let SalesforceID;
    const LogName = `${env.DEV_MODE ? '[DEV] ' : ''} ${status}`;

    switch (status) {
      case 'PREQUAL_APPROVED':
      case 'PREQUAL_DENIED':
      case 'PREQUAL_FAST_TRACK':
        Deal__c = {
          Stage__c: SalesforceUtils.getDealStage(status),
          Portal_Status__c: status,
          Method__c: 'Portal',
          // Prequal Fields
          Funding_Purpose__c: fields.purpose,
          Funding_Timeline__c: fields.timeline,
          Top_Priority__c: fields.topPriority,
          Estimated_Fico__c: fields.estimatedFICO,
          Monthly_Revenue_Range__c: fields.monthlyRevenue,
          Business_Revenue__c: SalesforceUtils.parseMonthlyRevenue(fields.monthlyRevenue),
          Credit_Score_Owner1__c: SalesforceUtils.getCreditScore(fields.estimatedFICO),
          First_Name_O1__c: fields.firstName,
          Last_Name_O1__c: fields.lastName,
          Email__c: fields.email,
          Phone__c: fields.phone,
          Business_Phone__c: fields.phone,
          Requested_Amount__c: fields.fundingAmount,
          Business_Legal_Name__c: fields.businessName,
          Business_Start_Date__c: fields.businessStartDate,
          // App Details
          UUID__c: uuid,
          FastTrack__c: fastTrack,
          Domain__c: domain,
          OwnerId: agent?.id,
          PortalRep__c: agent?.id,
          Date_Moved_to_Interested__c: created_at,
          Denial_Reason__c: reason || null,
          Pre_Qual_Amount__c: approvalAmount,
          App_Resume_URL__c: `${appPortalUrl}/${portalPath}/${uuid}`,
          // Metadata
          IP__c: initiated.ip,
          Country__c: initiated.country,
          City__c: initiated.city,
          Timezone__c: initiated.timezone,
          Browser__c: initiated.userAgent?.browser?.name || null,
          Device__c: deviceInfo.filter(Boolean).join(' '),
          // UTM
          UTM_Source__c: utm?.utm_source || null,
          UTM_Campaign__c: utm?.utm_campaign || null,
          UTM_Site__c: utm?.utm_site || null,
          UTM_Rep__c: utm?.utm_rep || null,
          UTM_Affiliate__c: utm?.utm_af || null,
          // Prequal Form Duration
          Prequal_Duration__c: parseInt(utm.utm_dur) || null,
        };

        if (env.DEV_MODE) {
          Deal__c.Business_Legal_Name__c = `[DEV] ${fields.businessName}`;
          Deal__c.Email__c = `DEV-${fields.email}`;
        }

        log('Creating Deal in SF:', Deal__c);

        const data = await SalesforceClient.createDeal(env, Deal__c);
        SalesforceID = data.id;

        log('Deal Created in SF', JSON.stringify(data));
        log('Deal ID:', SalesforceID);
        await putAppSalesforceID(env, uuid, data);

        await SalesforceClient.APIUpdateLog(env, SalesforceID, LogName, SalesforceUtils.printMeta(meta.initiated), application);
        break;

      case 'APP_SUBMITTED':
        const { address, owners } = applicationFields;
        const pii = await getAppPII(env, uuid);
        const hasTwoOwners = owners.length === 2;

        Deal__c = {
          Stage__c: SalesforceUtils.getDealStage(status),
          Portal_Status__c: status,
          // App Fields
          Business_Legal_Name__c: applicationFields.businessName,
          Business_DBA_Name__c: applicationFields.dbaName,
          Website__c: applicationFields.website,
          Legal_Entity_Type__c: applicationFields.entityType,
          Industry__c: applicationFields.industry,
          Business_Start_Date__c: applicationFields.businessStartDate,
          Business_Phone__c: applicationFields.phone,
          Business_Email__c: applicationFields.businessEmail,
          Email__c: applicationFields.email,
          Phone__c: applicationFields.phone,
          Business_Street__c: `${address.line1} ${address.line2}`,
          Business_City__c: address.city,
          Business_State__c: address.state,
          Business_Zip__c: address.zip,
          // Owner Fields
          First_Name_O1__c: owners[0].firstName,
          Last_Name_O1__c: owners[0].lastName,
          Email_O1__c: owners[0].email,
          Phone_O1__c: owners[0].phone,
          Home_Address_O1__c: `${owners[0].address.line1} ${owners[0].address.line2}`,
          Home_City_O1__c: owners[0].address.city,
          Home_State_O1__c: owners[0].address.state,
          Home_Zip_O1__c: owners[0].address.zip,
          Percent_of_Ownership_O1__c: owners[0].ownershipPercentage,
          // PII
          EIN__c: pii?.ein,
          SSN_O1__c: pii?.owners[0]?.ssn,
          DOB_O1__c: pii?.owners[0]?.dateOfBirth,
        };
        // If there are two owners on the deal, inject O2 fields
        if (hasTwoOwners) {
          const owner2Fields = {
            First_Name_O2__c: owners[1].firstName,
            Last_Name_O2__c: owners[1].lastName,
            Email_O2__c: owners[1].email,
            Phone_O2__c: owners[1].phone,
            Home_Address_O2__c: `${owners[1].address.line1} ${owners[1].address.line2}`,
            Home_City_O2__c: owners[1].address.city,
            Home_State_O2__c: owners[1].address.state,
            Home_Zip_O2__c: owners[1].address.zip,
            Percent_of_Ownership_O2__c: owners[1].ownershipPercentage,
            // Owner #2 PII
            SSN_O2__c: pii?.owners[1]?.ssn,
            DOB_O2__c: pii?.owners[1]?.dateOfBirth,
          };
          Object.assign(Deal__c, owner2Fields);
        }

        if (env.DEV_MODE) {
          Deal__c.Business_Legal_Name__c = `[DEV] ${applicationFields.businessName}`;
          Deal__c.Email__c = `DEV-${applicationFields.businessEmail}`;
        }

        SalesforceID = await getAppSalesforceID(env, uuid);
        // PII Fields are masked here so they aren't present in logs etc.
        log(`Updating Deal ${SalesforceID} in SF:`, SalesforceUtils.maskFields(Deal__c));
        await SalesforceClient.updateDeal(env, SalesforceID, Deal__c);
        await SalesforceClient.APIUpdateLog(env, SalesforceID, LogName, SalesforceUtils.printMeta(meta.submitted), application);

        break;

      case 'APP_SIGNED':
        SalesforceID = await getAppSalesforceID(env, uuid);
        Deal__c = { Stage__c: SalesforceUtils.getDealStage(status), Portal_Status__c: status };

        const signedAppBase64 = await downloadDocumentWithoutSignature(env, pandadoc.document.id);
        const fileName = `App - ${applicationFields.businessName}.pdf`;

        const linkedFile = await SalesforceClient.uploadLinkedFile(env, SalesforceID, fileName, signedAppBase64);
        log(fileName, 'uploaded and linked to deal', linkedFile);

        log(`Updating Deal ${SalesforceID} in SF:`, Deal__c);
        await SalesforceClient.updateDeal(env, SalesforceID, Deal__c);
        await SalesforceClient.APIUpdateLog(env, SalesforceID, LogName, SalesforceUtils.printMeta(meta.signed), application);

        break;

      case 'APP_COMPLETED':
        SalesforceID = await getAppSalesforceID(env, uuid);

        const { hasBankStatements } = message;
        Deal__c = { Stage__c: SalesforceUtils.getDealStage(status, hasBankStatements), Portal_Status__c: status };

        if (hasBankStatements) {
          log('Uploading bank statements');
          const storedBankStatements = await getAppBankStatements(env, uuid);

          await Promise.all(
            storedBankStatements.map(({ name, dataUrl }) => {
              const base64Content = dataUrl.replace(/^data:.*;base64,/, '');
              return SalesforceClient.uploadLinkedFile(env, SalesforceID, name, base64Content);
            })
          );
        } else {
          log('No bank statements uploaded');
        }

        log(`Updating Deal ${SalesforceID} in SF:`, Deal__c);
        await SalesforceClient.updateDeal(env, SalesforceID, Deal__c);
        await SalesforceClient.APIUpdateLog(env, SalesforceID, LogName, SalesforceUtils.printMeta(meta.completed), application);

        break;

      case 'APP_STARTED':
        Deal__c = { Portal_Status__c: status };
        SalesforceID = await getAppSalesforceID(env, uuid);

        log(`Updating Deal ${SalesforceID} in SF:`, Deal__c);
        await SalesforceClient.updateDeal(env, SalesforceID, Deal__c);
        await SalesforceClient.APIUpdateLog(env, SalesforceID, LogName, SalesforceUtils.printMeta(meta.started), application);
        break;
      case 'APP_EDITING':
        Deal__c = { Portal_Status__c: status };
        SalesforceID = await getAppSalesforceID(env, uuid);

        log(`Updating Deal ${SalesforceID} in SF:`, Deal__c);
        await SalesforceClient.updateDeal(env, SalesforceID, Deal__c);
        await SalesforceClient.APIUpdateLog(env, SalesforceID, LogName, SalesforceUtils.printMeta(meta.edited), application);
        break;
      default:
        log(`No action required for this status:`, status);
        break;
    }

    log('Salesforce processing completed successfully');
  } catch (error) {
    console.error('SALESFORCE:\t', `Error processing Salesforce data: ${error.message}`);
    throw error;
  }
}
