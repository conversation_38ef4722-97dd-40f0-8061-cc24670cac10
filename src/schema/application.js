import { z } from 'zod';

const address = z.object({
  line1: z.string().min(2).max(100),
  line2: z.string().max(100).optional(),
  city: z.string().min(2).max(100),
  state: z.string().length(2),
  zip: z.string().length(5),
});

const owner = z.object({
  firstName: z.string().min(2).max(50),
  lastName: z.string().min(2).max(50),
  dateOfBirth: z.string().date(),
  ssn: z.string().regex(/^\d{9}$/),
  email: z.string().email(),
  phone: z.string(),
  address: address,
  ownershipPercentage: z.number().min(0).max(100),
});

const fileSchema = z.object({
  name: z.string(),
  type: z.literal('application/pdf'),
  size: z.number(),
  dataUrl: z.string(),
  preview: z.string().url(),
  lastModified: z.number(),
});

export const applicationSchema = z.object({
  businessName: z.string().min(2).max(100),
  dbaName: z.string().optional(),
  website: z.string().optional(),
  entityType: z.string().min(2).max(100),
  ein: z.string().regex(/^\d{9}$/),
  industry: z.string().min(2).max(100),
  businessStartDate: z.string().date(),
  businessPhone: z.string(),
  businessEmail: z.string().email(),
  address: address,
  owners: z.array(owner).min(1, { message: 'At least one owner is required' }),
  currentStep: z.number().int().min(0).optional(),
});

export const bankStatementsSchema = z.object({
  bankStatements: z.union([z.array(fileSchema).length(0), z.array(fileSchema).length(3)]).optional(),
});
