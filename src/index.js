import { Hono } from 'hono';

import { corsMiddleware } from './middlewares/cors';
import { rateLimitMiddleware } from './middlewares/rate-limit';
import { apiBodyLimitMiddleware } from './middlewares/body-limit';
import { attachTimestampMiddleware } from './middlewares/attach-timestamp';
import { emailQueueHandler } from './queues/email';
import { salesforceQueueHandler } from './queues/salesforce';
import { adminQueueHandler } from './queues/admin';
import { createtAppHandlers } from './handlers/app/create-app';
import { createAppFasttrackHandlers } from './handlers/app/create-app-fasttrack';
import { getAppHandlers } from './handlers/app/get-app';
import { submitAppHandlers } from './handlers/app/submit-app';
import { editAppHandlers } from './handlers/app/edit-app';
import { signAppHandlers } from './handlers/app/sign-app';
import { startAppHandlers } from './handlers/app/start-app';
import { completeAppHandlers } from './handlers/app/complete-app';
import { pandadocStatusHandlers } from './handlers/app/pandadoc-status';
import { validateRepHandlers } from './handlers/validate-rep';
import { errorHandler } from './handlers/error/error-handler';
import { SalesforceClient, salesforceApiRequest } from './salesforce';
import { exportDataHandlers } from './handlers/export-data';
import { d1MigrationHandlers } from './handlers/d1-migrations/migrate';
import { logErrorToKV } from './kv/logs';
import { getAppPII, getAppBankStatements } from './kv';
import { getAppSalesforceID } from './utils/helpers';

const app = new Hono();

app.use('*', corsMiddleware);
app.use('*', rateLimitMiddleware);
app.use('*', apiBodyLimitMiddleware);
app.use('*', attachTimestampMiddleware);

app.get('/', (c) => c.text('OK'));

app.post('/app', ...createtAppHandlers);
app.post('/app/fasttrack', ...createAppFasttrackHandlers);
app.get('/app/:uuid', ...getAppHandlers);

app.post('/app/:uuid/submit', ...submitAppHandlers);
app.post('/app/:uuid/edit', ...editAppHandlers);
app.post('/app/:uuid/start', ...startAppHandlers);
app.post('/app/:uuid/sign', ...signAppHandlers);
app.post('/app/:uuid/complete', ...completeAppHandlers);

app.get('/app/:uuid/pandadoc/status', ...pandadocStatusHandlers);

app.get('/reps/:rep/validate', ...validateRepHandlers);

app.get('/migrate', ...d1MigrationHandlers);

app.get('/export', ...exportDataHandlers);

// TODO: remove these methods from prod - used for debugging
app.get('/sf/objects', async (c) => {
  const data = await SalesforceClient.objects(c.env);
  return c.json(data);
});

app.get('/app/:uuid/debug', async (c) => {
  const { uuid } = c.req.param();
  const app = await c.env.KV.get(`app:${uuid}`, { type: 'json' });
  const pii = await getAppPII(c.env, uuid);
  const bankStatements = await getAppBankStatements(c.env, uuid);
  const SalesforceID = await getAppSalesforceID(env, uuid);
  return c.json({ app, pii, bankStatements, SalesforceID });
});

app.get('/sf/objects/:object', async (c) => {
  const { object } = c.req.param();
  const data = await SalesforceClient.describeObject(c.env, object);
  return c.json(data);
});

app.get('/sf/:object/:recordId', async (c) => {
  const { object, recordId } = c.req.param();
  const v = c.env.SALESFORCE_API_VERSION;
  const path = `/services/data/${v}/sobjects/${object}/${recordId}`;
  const data = await salesforceApiRequest(c.env, 'GET', path);
  return c.json(data);
});

app.get('/sf/objects/:object/formatted', async (c) => {
  const { object } = c.req.param();
  const data = await SalesforceClient.describeObject(c.env, object);
  const formatted = data.fields.map((f) =>
    Object.fromEntries(
      ['label', 'name', 'type', 'custom', 'length', 'referenceTo', 'relationshipName', 'picklistValues']
        .filter((k) => k in f)
        .map((k) => [k, f[k]])
    )
  );
  return c.json(formatted);
});

app.notFound((c) => c.text('Not Found', 404));

app.onError(errorHandler);

export default {
  fetch: app.fetch,

  async queue(batch, env) {
    // disable queue while running tests
    const IS_TESTING = import.meta.env ? import.meta.env?.MODE === 'test' : false;
    if (IS_TESTING) {
      return;
    }
    console.log(`[QUEUE] Processing ${batch.messages.length} messages from queue: ${batch.queue}`);

    const messagePromises = batch.messages.map(async (message) => {
      console.log(`[QUEUE] Processing message:`, JSON.stringify(message));
      try {
        // Parse the message body if it's a string
        const data = typeof message.body === 'string' ? JSON.parse(message.body) : message.body;

        let handler;
        if (batch.queue.includes('-email')) {
          handler = emailQueueHandler;
          console.log(`[QUEUE] Processing email queue message`);
        } else if (batch.queue.includes('-salesforce')) {
          handler = salesforceQueueHandler;
          console.log(`[QUEUE] Processing salesforce queue message`);
        } else if (batch.queue.includes('-admin')) {
          handler = adminQueueHandler;
          console.log(`[QUEUE] Processing admin queue message`);
        } else {
          console.warn(`[QUEUE] Unknown queue: ${batch.queue}`);
          message.ack();
          return;
        }

        await handler(data, env);

        // Acknowledge the message was processed successfully
        message.ack();
      } catch (error) {
        const { errorId } = await logErrorToKV(env, error, {
          source: error?.source || 'queueHandler',
          statusCode: error?.statusCode || 500,
        });

        console.error(`[QUEUE] Error processing message:${errorId} - ${error?.message}`);
        // Retry the message
        message.retry();
      }
    });

    await Promise.all(messagePromises);
  },
};
