import { createFactory } from 'hono/factory';
import { ensureApplicationByUUID } from './get-app';
import { cleanedApplication, AppError } from '../../utils/helpers';
import { moveDocumentToDraft } from '../../pandadoc';
import { updateApplication } from '../../db/applications';
import { getMeta } from '../../utils';
import { sendToSalesforceQueue } from '../../queues';

const factory = createFactory();

export const editAppHandlers = factory.createHandlers(ensureApplicationByUUID, async (c) => {
  const timestamp = c.get('timestamp');
  const application = c.get('application');

  if (application.status === 'APP_SUBMITTED') {
    const document = await moveDocumentToDraft(c.env, application.pandadoc?.document?.id);
    application.pandadoc = { document };

    application.status = 'APP_EDITING';
    application.edited_at = timestamp;

    if (!application.meta) {
      application.meta = {};
    }
    application.meta.edited = getMeta(c.req.raw, timestamp);

    await updateApplication(c.env, application.uuid, application, timestamp);
    c.executionCtx.waitUntil(sendToSalesforceQueue(c.env, { application }, { delaySeconds: 5 }));

    return c.json({ data: cleanedApplication(application) });
  } else {
    console.error(`Application status ${application.status} can't be edited`);
    throw new AppError(`Application can't be edited`, 400, 'editApp', `Status is ${application.status}`);
  }
});
