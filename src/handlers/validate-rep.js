import { createFactory } from 'hono/factory';
import { getAgentsListFromD1 } from '../db/meta';
import { AppError } from '../utils/helpers';

const factory = createFactory();

export const validateRep = (agentsList, rep) => {
  if (rep.toLowerCase() === 'test') return true;
  const isValidRep = agentsList.some((agent) => {
    const emailPrefix = agent.email.split('@')[0];
    return emailPrefix.toLowerCase() === rep.toLowerCase();
  });

  return isValidRep;
};

export const validateRepHandlers = factory.createHandlers(async (c) => {
  const rep = c.req.param('rep')?.trim().toLowerCase();

  if (!rep) {
    throw new AppError('Missing rep parameter', 400, 'validationError');
  }

  const agentsList = await getAgentsListFromD1(c.env);
  console.log('Valid Reps:', agentsList.map((o) => o.email.split('@')[0]).join(', '));

  const isValidRep = validateRep(agentsList, rep);
  console.log(`'${rep}'`, 'is valid:', isValidRep);

  return c.json({
    rep,
    valid: isValidRep,
  });
});
