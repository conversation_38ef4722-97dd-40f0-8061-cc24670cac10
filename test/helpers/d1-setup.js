export async function setupD1Database(env) {
  // Create the applications table
  await env.DB.prepare(
    `
    CREATE TABLE IF NOT EXISTS applications (
      uuid TEXT PRIMARY KEY,
      version INTEGER NOT NULL,
      status TEXT NOT NULL,
      domain TEXT NOT NULL,
      preQualifyFields TEXT NOT NULL,
      approvalAmount INTEGER DEFAULT 0,
      agent TEXT,
      reason TEXT,
      applicationFields TEXT,
      pandadoc TEXT,
      utm TEXT,
      meta TEXT,
      fastTrack INTEGER NOT NULL DEFAULT 0,
      salesforce_id VARCHAR(18) UNIQUE,
      created_at TEXT DEFAULT (datetime('now')),
      updated_at TEXT DEFAULT (datetime('now')),
      started_at TEXT,
      submitted_at TEXT,
      signed_at TEXT,
      completed_at TEXT
    )
  `
  ).run();

  // Create the meta table
  await env.DB.prepare(
    `
    CREATE TABLE IF NOT EXISTS meta (
      key TEXT PRIMARY KEY,
      value TEXT NOT NULL,
      created_at TEXT DEFAULT (datetime('now')),
      updated_at TEXT DEFAULT (datetime('now'))
    )
  `
  ).run();

  // Insert default round-robin data
  await env.DB.prepare(
    `
    INSERT OR IGNORE INTO meta (key, value) VALUES
      ('round-robin:index', '0'),
      ('agents:list', '[]')
  `
  ).run();

  // Create the trigger for auto-updating updated_at
  await env.DB.prepare(
    `
    CREATE TRIGGER IF NOT EXISTS update_applications_updated_at
    AFTER UPDATE ON applications
    FOR EACH ROW
    BEGIN
      UPDATE applications SET updated_at = datetime('now') WHERE uuid = OLD.uuid;
    END
  `
  ).run();
}

export async function clearD1Database(env) {
  await env.DB.prepare('DELETE FROM applications').run();
}
